"""
Image Generator
-------------
Generates images using a Image Generation model via deepinfra.com API.
Supports parallel image generation with rate limiting.
"""

import os
import time
import base64
import logging
import asyncio
import traceback
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import List, Optional, Dict, Any, Tuple

import aiohttp
import requests
from tqdm import tqdm

from models.schema import ImagePrompt, EnhancedImagePrompt
from utils.content_moderation import sanitize_prompt

logger = logging.getLogger(__name__)


class ImageGenerator:
    def __init__(self,
                 model: str = "black-forest-labs/FLUX-1-schnell",
                 provider: str = "deepinfra",
                 max_concurrent_requests: int = 200,
                 use_parallel: bool = True):
        """
        Initialize the image generator with necessary API keys and configuration.

        Args:
            model (str): Image generation model to use.
            provider (str): Image generation provider (e.g., "deepinfra", "bfl").
            max_concurrent_requests (int): Maximum number of concurrent requests. Defaults to 200.
            use_parallel (bool): Whether to use parallel processing. Defaults to True.
        """
        self.provider = provider
        self.model = model
        self.max_concurrent_requests = max_concurrent_requests
        self.use_parallel = use_parallel

        if self.provider == "deepinfra":
            self.api_key = os.getenv("DEEPINFRA_API_KEY")

            if not self.api_key:
                raise ValueError("DEEPINFRA_API_KEY environment variable not set.")

            self.base_url = "https://api.deepinfra.com/v1/openai/images/generations"

        elif self.provider == "bfl":
            self.api_key = os.getenv("BFL_API_KEY")

            if not self.api_key:
                raise ValueError("BFL_API_KEY environment variable not set.")

            self.base_url = f"https://api.bfl.ai/v1/{self.model}"

        else:
            raise ValueError(f"Unsupported image provider: {self.provider}")

        self._semaphore = None
        self.max_retries = 10
        self.retry_delay = 3  # seconds

    def _generate_image_from_prompt(self,
                                    prompt: str,
                                    reference_image_path: Optional[str] = None,
                                    width: int = 1024,
                                    height: int = 1024,
                                    image_style: str = 'realistic') -> Optional[bytes]:
        """
        Generate an image using the model (synchronous version).

        Args:
            prompt (str): The image prompt
            reference_image_path (str, optional): Path to a reference image for image-to-image generation
            width (int, optional): Width of the generated image. Defaults to 1024.
            height (int, optional): Height of the generated image. Defaults to 1024.
            image_style (str, optional): Visual aesthetic style for the image. Defaults to 'realistic'.

        Returns:
            Optional[bytes]: The generated image data or None if generation fails
        """

        # Apply content moderation to the prompt first
        sanitized_prompt = sanitize_prompt(prompt)

        # Add style guidance to the prompt if not already included
        if image_style.lower() not in sanitized_prompt.lower():
            styled_prompt = f"{sanitized_prompt}, {image_style} style"
        else:
            styled_prompt = sanitized_prompt

        payload = {}

        if self.provider == "deepinfra":
            size = f"{width}x{height}"

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "prompt": styled_prompt,
                "model": self.model,
                "n": 1,
                "size": size,
                "response_format": "b64_json"
            }

            if reference_image_path:
                logger.warning("DeepInfra (OpenAI-compatible) API does not directly support image-to-image. Using text-to-image with prompt enhancement.")
                payload["prompt"] = f"{styled_prompt} (consistent with previous image, {image_style} style)"

        elif self.provider == "bfl":
            headers = {
                "X-Key": f"{self.api_key}",
                "Content-Type": "application/json"
            }

            # BFL specific payload structure with flux-kontext-pro support
            payload = {
                "prompt": styled_prompt,
                "safety_tolerance": 6,
                "output_format": "png",
                "aspect_ratio": "16:9",
                "prompt_upsampling": False,
            }

            # Handle reference image for flux-kontext-pro model
            if reference_image_path:
                try:
                    with open(reference_image_path, "rb") as f:
                        encoded_image = base64.b64encode(f.read()).decode('utf-8')

                    # Use appropriate field name based on model
                    if "flux-kontext-pro" in self.model.lower():
                        payload["reference_image"] = encoded_image
                        logger.info(f"Using reference image for flux-kontext-pro: {reference_image_path}")
                    else:
                        payload["input_image"] = encoded_image
                        logger.info(f"Using reference image for {self.model}: {reference_image_path}")

                except FileNotFoundError:
                    logger.error(f"Reference image not found: {reference_image_path}. Proceeding without it.")

                except Exception as e:
                    logger.error(f"Error encoding reference image {reference_image_path}: {e}. Proceeding without it.")

        else:
            raise ValueError(f"Unsupported image provider: {self.provider}")

        try:
            # Send the request and handle the response
            response = requests.post(self.base_url, headers=headers, json=payload)
            response.raise_for_status()
            result = response.json()

            print(payload["prompt"])
            print(response.json())
            print()

            if self.provider == "deepinfra":
                if "data" in result and len(result["data"]) > 0 and "b64_json" in result["data"][0]:
                    # DeepInfra response is valid, decode and return
                    image_data = base64.b64decode(result["data"][0]["b64_json"])
                    return image_data

                else:
                    # Unexpected DeepInfra API response
                    logger.error(f"Unexpected DeepInfra API response: {result}")
                    return None

            elif self.provider == "bfl":
                # BFL response contains a polling URL
                polling_url = result["polling_url"]

                # Initial delay before polling
                time.sleep(self.retry_delay)

                for _ in range(self.max_retries):
                    # Poll the polling URL
                    polling_response = requests.get(polling_url)
                    polling_response.raise_for_status()

                    result = polling_response.json()["result"]

                    if result and "sample" in result:
                        # Image is ready, download it
                        image_response = requests.get(result["sample"])

                        # Check if the request was successful
                        if not image_response.ok:
                            logger.error(f"Failed to download image: {image_response.text}")
                            return None

                        return image_response.content

                    # Image is not ready, wait and retry
                    print(polling_response.json())
                    logger.warning(f"Image not ready, retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)

                # Image not ready after retries, return None
                logger.error(f"Image not ready after {self.max_retries} retries.")
                return None

        except Exception as e:
            traceback.print_exc()
            logger.error(f"Error generating image with {self.provider}: {str(e)}")
            return None

    async def _generate_image_from_prompt_async(self,
                                                prompt: str,
                                                reference_image_path: Optional[str] = None,
                                                width: int = 1024,
                                                height: int = 1024,
                                                image_style: str = 'realistic',
                                                retry_count: int = 0) -> Optional[bytes]:
        """
        Generate an image using the model (asynchronous version).

        Args:
            prompt (str): The image prompt
            reference_image_path (str, optional): Path to a reference image for image-to-image generation
            width (int, optional): Width of the generated image. Defaults to 1024.
            height (int, optional): Height of the generated image. Defaults to 1024.
            image_style (str, optional): Visual aesthetic style for the image. Defaults to 'realistic'.
            retry_count (int, optional): Current retry attempt. Defaults to 0.

        Returns:
            Optional[bytes]: The generated image data or None if generation fails
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # Apply content moderation to the prompt first
        sanitized_prompt = sanitize_prompt(prompt)

        # Add style guidance to the prompt if not already included
        if image_style.lower() not in sanitized_prompt.lower():
            styled_prompt = f"{sanitized_prompt}, {image_style} style"
        else:
            styled_prompt = sanitized_prompt

        payload = {}

        if self.provider == "deepinfra":
            size = f"{width}x{height}"

            payload = {
                "prompt": styled_prompt,
                "model": self.model,
                "n": 1,
                "size": size,
                "response_format": "b64_json"
            }

            if reference_image_path:
                logger.warning("DeepInfra (OpenAI-compatible) API does not directly support image-to-image. Using text-to-image with prompt enhancement.")
                payload["prompt"] = f"{styled_prompt} (consistent with previous image, {image_style} style)"

        elif self.provider == "bfl":
            # BFL specific payload structure with flux-kontext-pro support
            payload = {
                "prompt": styled_prompt,
                "safety_tolerance": 2,
                "output_format": "png",
                "aspect_ratio": "16:9",
                "prompt_upsampling": False,
            }

            if reference_image_path:
                try:
                    # Read and encode the reference image asynchronously
                    loop = asyncio.get_running_loop()

                    with open(reference_image_path, "rb") as f:
                        encoded_image = await loop.run_in_executor(None, lambda: base64.b64encode(f.read()).decode('utf-8'))

                    # Use appropriate field name based on model
                    if "flux-kontext-pro" in self.model.lower():
                        payload["reference_image"] = encoded_image
                        logger.info(f"Using reference image for flux-kontext-pro: {reference_image_path}")
                    else:
                        payload["reference_image"] = encoded_image
                        logger.info(f"Using reference image for {self.model}: {reference_image_path}")

                except FileNotFoundError:
                    logger.error(f"Reference image not found: {reference_image_path}. Proceeding without it.")

                except Exception as e:
                    logger.error(f"Error encoding reference image {reference_image_path}: {e}. Proceeding without it.")
        else:
            raise ValueError(f"Unsupported image provider: {self.provider}")

        try:
            async with self._semaphore:
                async with aiohttp.ClientSession() as session:
                    async with session.post(self.base_url, headers=headers, json=payload) as response:

                        response.raise_for_status()
                        result = await response.json()

                        if self.provider == "deepinfra":

                            if "data" in result and len(result["data"]) > 0 and "b64_json" in result["data"][0]:
                                image_data = base64.b64decode(result["data"][0]["b64_json"])
                                return image_data

                            else:
                                logger.error(f"Unexpected DeepInfra API response: {result}")
                                return None

                        elif self.provider == "bfl":

                            if "image" in result:
                                image_data = base64.b64decode(result["image"])
                                return image_data

                            else:
                                logger.error(f"Unexpected BFL API response: {result}")
                                return None

        except aiohttp.ClientResponseError as e:

            if e.status in (429, 500, 502, 503, 504) and retry_count < self.max_retries:
                retry_count += 1
                wait_time = self.retry_delay * (2 ** (retry_count - 1))

                logger.warning(f"Rate limited or server error ({e.status}). Retrying in {wait_time}s (attempt {retry_count}/{self.max_retries})")

                await asyncio.sleep(wait_time)
                return await self._generate_image_from_prompt_async(prompt, reference_image_path, width, height, image_style, retry_count)

            logger.error(f"Error generating image (HTTP {e.status}) with {self.provider}: {str(e)}")
            return None

        except Exception as e:
            logger.error(f"Error generating image with {self.provider}: {str(e)}")
            return None

    async def _process_segment_async(self,
                                     segment: ImagePrompt,
                                     output_dir: str,
                                     width: int,
                                     height: int,
                                     image_style: str,
                                     reference_image: Optional[str] = None) -> Optional[str]:
        """
        Process a single segment asynchronously.

        Args:
            segment (ImagePrompt): The segment to process
            output_dir (str): Directory to save the image
            width (int): Width of the generated image
            height (int): Height of the generated image
            image_style (str): Visual aesthetic style for the image
            reference_image (Optional[str], optional): Path to a reference image. Defaults to None.

        Returns:
            Optional[str]: Path to the generated image or None if generation fails
        """
        scene_num = segment.scene_number
        segment_num = segment.segment_number
        prompt = segment.image_prompt

        if not prompt:
            logger.warning(f"Empty prompt for segment {scene_num}-{segment_num}, skipping")
            return None

        # Apply content moderation to the prompt first
        sanitized_prompt = sanitize_prompt(prompt)

        # Enhance the prompt with style-specific keywords if not already included
        if image_style.lower() not in sanitized_prompt.lower():
            style_prompt = f"{sanitized_prompt}, {image_style} style"
        else:
            style_prompt = sanitized_prompt

        # Generate a unique filename
        filename = os.path.join(output_dir, f"scene_{scene_num}_segment_{segment_num}.png")

        try:
            # Generate the image
            image_data = await self._generate_image_from_prompt_async(
                style_prompt, reference_image, width, height, image_style
            )

            if image_data:
                # Save the image to a file
                with open(filename, "wb") as f:
                    f.write(image_data)

                logger.info(f"Image saved to {filename}")
                return filename

            else:
                logger.error(f"Failed to generate image for segment {scene_num}-{segment_num}")
                return None

        except Exception as e:
            logger.error(f"Error generating image for segment {scene_num}-{segment_num}: {str(e)}")
            return None

    async def generate_images_async(self,
                                    scene_segments: List[ImagePrompt],
                                    output_dir: str = "assets/images",
                                    width: int = 1024,
                                    height: int = 1024,
                                    image_style: str = 'realistic') -> List[str]:
        """
        Generate images for each scene segment asynchronously.

        Args:
            scene_segments (List[ImagePrompt]): List of scene segments with image prompts
            output_dir (str, optional): Directory to save images. Defaults to "assets/images".
            width (int, optional): Width of the generated images. Defaults to 1024.
            height (int, optional): Height of the generated images. Defaults to 1024.
            image_style (str, optional): Visual aesthetic style for the images. Defaults to 'realistic'.

        Returns:
            List[str]: List of image file paths
        """
        logger.info(f"Starting parallel image generation with dimensions {width}x{height} in {image_style} style")
        logger.info(f"Using up to {self.max_concurrent_requests} concurrent requests")

        # Create the images directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Initialize the semaphore for rate limiting
        self._semaphore = asyncio.Semaphore(self.max_concurrent_requests)

        # Create tasks for all segments
        tasks = []
        reference_image = None  # Initial reference image is None

        # Create a progress bar
        with tqdm(total=len(scene_segments), desc="Generating images") as pbar:

            # First, create all the tasks
            for segment in scene_segments:
                # Create a task for this segment
                task = asyncio.create_task(
                    self._process_segment_async(segment, output_dir, width, height, image_style, reference_image)
                )

                # Add a callback to update the progress bar
                task.add_done_callback(lambda _: pbar.update(1))
                tasks.append(task)

            # Wait for all tasks to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out None values and exceptions
        image_paths = []

        for i, result in enumerate(results):

            if isinstance(result, Exception):
                logger.error(f"Error processing segment {i}: {str(result)}")

            elif result is not None:
                image_paths.append(result)

        logger.info(f"Generated {len(image_paths)} images out of {len(scene_segments)} segments")
        return image_paths

    def generate_images(self,
                        scene_segments: List[ImagePrompt],
                        output_dir: str = "assets/images",
                        width: int = 1024,
                        height: int = 1024,
                        image_style: str = 'realistic',
                        reference_image_path: Optional[str] = None) -> List[str]:
        """
        Generate images for each scene segment.

        Args:
            scene_segments (List[ImagePrompt]): List of scene segments with image prompts
            output_dir (str, optional): Directory to save images. Defaults to "assets/images".
            width (int, optional): Width of the generated images. Defaults to 1024.
            height (int, optional): Height of the generated images. Defaults to 1024.
            image_style (str, optional): Visual aesthetic style for the images. Defaults to 'realistic'.
            reference_image_path (str, optional): Path to a reference image for image-to-image generation.
                                                  This will be used for all images if provided.

        Returns:
            List[str]: List of image file paths
        """
        # Use the instance variable for parallel processing
        use_parallel = self.use_parallel

        if use_parallel:
            # Use the async version with asyncio.run
            return asyncio.run(self.generate_images_async(
                scene_segments, output_dir, width, height, image_style, reference_image_path
            ))

        # Otherwise, use the original sequential implementation
        logger.info(f"Starting sequential image generation with dimensions {width}x{height} in {image_style} style")

        if reference_image_path:
            logger.info(f"Using global reference image: {reference_image_path}")

        image_paths = []

        # Create the images directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # If a global reference image is provided, use it for all generations
        # Otherwise, for sequential generation, the reference image will be the previously generated image
        current_reference_image = reference_image_path

        # Process each segment
        for i, segment in enumerate(scene_segments):
            scene_num = segment.scene_number
            segment_num = segment.segment_number
            prompt = segment.image_prompt

            if not prompt:
                logger.warning(f"Empty prompt for segment {scene_num}-{segment_num}, skipping")
                continue

            logger.info(f"Generating {image_style} style image for segment {i+1}/{len(scene_segments)}")

            # Apply content moderation to the prompt first
            sanitized_prompt = sanitize_prompt(prompt)

            # Enhance the prompt with style-specific keywords if not already included
            if image_style.lower() not in sanitized_prompt.lower():
                style_prompt = f"{sanitized_prompt}, {image_style} style"
            else:
                style_prompt = sanitized_prompt

            # Generate a unique filename
            filename = os.path.join(output_dir, f"scene_{scene_num}_segment_{segment_num}.png")

            try:
                # Generate the image
                image_data = self._generate_image_from_prompt(style_prompt, current_reference_image, width, height, image_style)

                if image_data:
                    # Save the image to a file
                    with open(filename, "wb") as f:
                        f.write(image_data)

                    # Add the file path to the list
                    image_paths.append(filename)

                    # If no global reference image is set, update the reference image for the next generation
                    if not reference_image_path:
                        current_reference_image = filename

                    logger.info(f"Image saved to {filename}")

                else:
                    logger.error(f"Failed to generate image for segment {scene_num}-{segment_num}")

            except Exception as e:
                logger.error(f"Error generating image for segment {scene_num}-{segment_num}: {str(e)}")
                # Continue with the next segment

        logger.info(f"Image generation completed successfully, created {len(image_paths)} images")
        return image_paths

    def generate_images_with_character_consistency(self,
                                                   scene_segments: List[EnhancedImagePrompt],
                                                   output_dir: str = "assets/images",
                                                   width: int = 1024,
                                                   height: int = 1024,
                                                   image_style: str = 'realistic',
                                                   group_image_path: Optional[str] = None) -> List[str]:
        """
        Generate images for each scene segment with character consistency.

        Args:
            scene_segments (List[EnhancedImagePrompt]): List of enhanced scene segments with character data
            output_dir (str, optional): Directory to save images. Defaults to "assets/images".
            width (int, optional): Width of the generated images. Defaults to 1024.
            height (int, optional): Height of the generated images. Defaults to 1024.
            image_style (str, optional): Visual aesthetic style for the images. Defaults to 'realistic'.
            group_image_path (str, optional): Path to the group image containing all characters.

        Returns:
            List[str]: List of image file paths
        """
        logger.info(f"Starting character-consistent image generation with dimensions {width}x{height} in {image_style} style")

        if group_image_path:
            logger.info(f"Using group image for consistency: {group_image_path}")

        image_paths = []

        # Create the images directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Process each segment
        for i, segment in enumerate(scene_segments):
            scene_num = segment.scene_number
            segment_num = segment.segment_number
            prompt = segment.image_prompt

            if not prompt:
                logger.warning(f"Empty prompt for segment {scene_num}-{segment_num}, skipping")
                continue

            logger.info(f"Generating character-consistent {image_style} style image for segment {i+1}/{len(scene_segments)}")

            # Enhance the prompt with character consistency information
            enhanced_prompt = self._enhance_prompt_with_character_consistency(segment, image_style)

            # Generate a unique filename
            filename = os.path.join(output_dir, f"scene_{scene_num}_segment_{segment_num}.png")

            try:
                # Generate the image with enhanced prompt and group image as reference
                image_data = self._generate_image_from_prompt(
                    enhanced_prompt,
                    group_image_path, # Use the group image as reference
                    width,
                    height,
                    image_style
                )

                if image_data:
                    # Save the image to a file
                    with open(filename, "wb") as f:
                        f.write(image_data)

                    # Add the file path to the list
                    image_paths.append(filename)

                    logger.info(f"Character-consistent image saved to {filename}")

                    if segment.characters_in_scene:
                        logger.info(f"Characters in image: {', '.join(segment.characters_in_scene)}")

                else:
                    logger.error(f"Failed to generate character-consistent image for segment {scene_num}-{segment_num}")

            except Exception as e:
                logger.error(f"Error generating character-consistent image for segment {scene_num}-{segment_num}: {str(e)}")
                # Continue with the next segment

        logger.info(f"Character-consistent image generation completed successfully, created {len(image_paths)} images")
        return image_paths

    def _enhance_prompt_with_character_consistency(self, segment: EnhancedImagePrompt, image_style: str) -> str:
        """
        Enhance the image prompt with character consistency information optimized for flux-kontext-pro.

        Args:
            segment (EnhancedImagePrompt): Enhanced segment with character data
            image_style (str): Image style

        Returns:
            str: Enhanced prompt with character consistency for reference-based generation
        """
        base_prompt = segment.image_prompt

        # Apply content moderation to the base prompt first
        sanitized_base_prompt = sanitize_prompt(base_prompt)

        # If no characters in scene, return sanitized base prompt
        if not segment.characters_in_scene:
            # Ensure style is included
            if image_style.lower() not in sanitized_base_prompt.lower():
                return f"{sanitized_base_prompt}, {image_style} style"
            return sanitized_base_prompt

        # Create reference-based character consistency for flux-kontext-pro
        character_references = []
        for char_name in segment.characters_in_scene:
            # Reference characters by their labeled names from the reference image
            character_references.append(f"{char_name}")

        # Combine base prompt with character references for flux-kontext-pro
        if character_references:
            character_ref_text = ", ".join(character_references)
            enhanced_prompt = f"{sanitized_base_prompt}. Characters from reference image: {character_ref_text}. Maintain exact character appearance as shown in the reference image."
        else:
            enhanced_prompt = sanitized_base_prompt

        # Ensure style is included
        if image_style.lower() not in enhanced_prompt.lower():
            enhanced_prompt = f"{enhanced_prompt}, {image_style} style"

        # Add flux-kontext-pro specific instructions
        enhanced_prompt = f"{enhanced_prompt}. Use the labeled reference image to maintain character consistency and visual accuracy."

        return enhanced_prompt
