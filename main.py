"""
AI Hindi Story Video Generator
--------------------------------
An autonomous Python-based agent that creates, assembles, and uploads
10-minute Hindi story videos based on real incidents.
"""
import re
import os
import json
import glob
import logging
import argparse
import datetime
from typing import Optional, Tuple, List

# Import dotenv
from dotenv import load_dotenv

# Import agents
from agents.editor import EditorAgent
from agents.writer import WriterAgent
from agents.researcher import ResearcherAgent
from agents.story_editor import StoryEditorAgent
from agents.dialogue_splitter import DialogueSplitterAgent
from agents.image_desc_gen import ImageDescriptionGenerator
from agents.character_consistency import CharacterConsistencyAgent

# Import utilities
from utils.image_generator import ImageGenerator
from utils.video_assembler import VideoAssembler
from utils.tts_generator import create_tts_generator
from utils.agent_factory import create_rate_limited_agent

# Import schema models
from models.schema import Story, CharacterConsistencyData

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment(llm_provider: str = "openai",
                      dev_mode: bool = True,
                      image_provider: str = "deepinfra") -> None:
    """
    Load environment variables and create necessary directories.

    Args:
        llm_provider (str): The LLM provider to use. Either "openai" or "openrouter".
        dev_mode (bool): Whether to use development mode with local TTS.
            If True, ElevenLabs API key is not required.
        image_provider (str): The image generation provider to use. Either "deepinfra" or "bfl".

    Raises:
        ValueError: If an unsupported provider is specified.
        EnvironmentError: If required API keys are missing.
    """
    # Force reload environment variables
    load_dotenv(override=True)

    # Define required API keys based on provider and dev mode
    required_keys = [
        'SERPER_API_KEY'
    ]

    # Only require ElevenLabs API key if not in dev mode
    if not dev_mode:
        required_keys.append('ELEVENLABS_API_KEY')
        logger.info("Production mode: ElevenLabs API key required")
    else:
        logger.info("Development mode: ElevenLabs API key not required")

    # Add LLM provider-specific API key requirement
    if llm_provider.lower() == "openai":
        required_keys.append('OPENAI_API_KEY')
    elif llm_provider.lower() == "openrouter":
        required_keys.append('OPENROUTER_API_KEY')
    else:
        raise ValueError(f"Unsupported LLM provider: {llm_provider}. Use 'openai' or 'openrouter'.")

    # Add image provider-specific API key requirement
    if image_provider.lower() == "deepinfra":
        required_keys.append('DEEPINFRA_API_KEY')
    elif image_provider.lower() == "bfl":
        required_keys.append('BFL_API_KEY')
    else:
        raise ValueError(f"Unsupported image provider: {image_provider}. Use 'deepinfra' or 'bfl'.")

    missing_keys = [key for key in required_keys if not os.getenv(key)]
    if missing_keys:
        raise EnvironmentError(f"Missing required API keys: {', '.join(missing_keys)}")

    # Create the main assets directory if it doesn't exist
    os.makedirs('assets', exist_ok=True)

    logger.info("Environment setup complete")

def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments for the AI Hindi Story Video Generator.

    Returns:
        argparse.Namespace: Parsed command line arguments containing all configuration options.

    Raises:
        SystemExit: If required arguments are missing or invalid arguments are provided.
    """
    parser = argparse.ArgumentParser(description='Generate AI Hindi story videos based on real incidents or fictional stories')

    # Story content arguments
    parser.add_argument('--title', help='Title of the story (required unless --continue is used)')
    parser.add_argument('--story-type', dest='story_type', choices=['real', 'fictional', 'mixed'], default='real',
                        help='Type of story: "real" for stories based on real incidents, "fictional" for creative stories, "mixed" for combining real events with fictional storytelling')
    parser.add_argument('--genre', choices=['thriller', 'romance', 'mystery', 'action', 'drama', 'comedy', 'sci-fi', 'fantasy'],
                        default='thriller', help='Genre of the story')
    parser.add_argument('--references', nargs='+', help='URLs to articles, blogs, or reports to analyze for real or mixed stories')
    parser.add_argument('--context', help='Additional context for the story (required for fictional stories)')

    # Image generation arguments
    parser.add_argument('--image-style', dest='image_style', choices=['realistic', 'anime', 'cartoon', 'watercolor', 'oil-painting', 'sketch', 'noir', 'vintage'],
                        default='realistic', help='Visual aesthetic style for generated images')
    parser.add_argument('--image-generation', action='store_true', default=True,
                        help='Enable image generation (default: True)')
    parser.add_argument('--image-model', default='black-forest-labs/FLUX-1-schnell',
                        help='Model to use for image generation (default: black-forest-labs/FLUX-1-schnell)')
    parser.add_argument('--image-provider', choices=['deepinfra', 'bfl'], default='deepinfra',
                        help='Image generation provider to use (default: deepinfra)')
    parser.add_argument('--use-parallel-image-generation', dest='use_parallel_image_generation',
                        action='store_true', default=True,
                        help='Use parallel processing for image generation (default: True)')
    parser.add_argument('--no-parallel-image-generation', dest='use_parallel_image_generation', action='store_false',
                        help='Disable parallel processing for image generation')
    parser.add_argument('--max-concurrent-requests', dest='max_concurrent_requests', type=int, default=200,
                        help='Maximum number of concurrent requests for parallel image generation (default: 200)')

    # Audio generation arguments
    parser.add_argument('--elevenlabs-voice-id', default='MaBqnF6LpI8cAT5sGihk',
                        help='ElevenLabs voice ID to use for narration (default: MaBqnF6LpI8cAT5sGihk)')
    parser.add_argument('--dev', action='store_true', default=True,
                        help='Use development mode with local Coqui-AI TTS instead of ElevenLabs (default: True)')
    parser.add_argument('--no-dev', dest='dev', action='store_false',
                        help='Use production mode with ElevenLabs TTS instead of local Coqui-AI TTS')

    # LLM model arguments
    parser.add_argument('--model', default='gpt-4o-mini',
                        help='LLM model to use for all agents (default: gpt-4o-mini)')
    parser.add_argument('--provider', choices=['openai', 'openrouter'], default='openai',
                        help='LLM provider to use (default: openai)')
    parser.add_argument('--max-tokens-per-minute', dest='max_tokens_per_minute', type=int, default=30000,
                        help='Maximum tokens per minute for OpenAI API rate limiting (default: 30000)')

    # Agent-specific LLM model arguments
    parser.add_argument('--researcher-model', dest='researcher_model',
                        help='LLM model to use for ResearcherAgent (defaults to --model)')
    parser.add_argument('--researcher-provider', dest='researcher_provider', choices=['openai', 'openrouter'],
                        help='LLM provider to use for ResearcherAgent (defaults to --provider)')

    parser.add_argument('--editor-model', dest='editor_model',
                        help='LLM model to use for EditorAgent (defaults to --model)')
    parser.add_argument('--editor-provider', dest='editor_provider', choices=['openai', 'openrouter'],
                        help='LLM provider to use for EditorAgent (defaults to --provider)')

    parser.add_argument('--writer-model', dest='writer_model',
                        help='LLM model to use for WriterAgent (defaults to --model)')
    parser.add_argument('--writer-provider', dest='writer_provider', choices=['openai', 'openrouter'],
                        help='LLM provider to use for WriterAgent (defaults to --provider)')

    parser.add_argument('--dialogue-splitter-model', dest='dialogue_splitter_model',
                        help='LLM model to use for DialogueSplitterAgent (defaults to --model)')
    parser.add_argument('--dialogue-splitter-provider', dest='dialogue_splitter_provider', choices=['openai', 'openrouter'],
                        help='LLM provider to use for DialogueSplitterAgent (defaults to --provider)')

    parser.add_argument('--image-desc-gen-model', dest='image_desc_gen_model',
                        help='LLM model to use for ImageDescriptionGenerator (defaults to --model)')
    parser.add_argument('--image-desc-gen-provider', dest='image_desc_gen_provider', choices=['openai', 'openrouter'],
                        help='LLM provider to use for ImageDescriptionGenerator (defaults to --provider)')

    parser.add_argument('--story-editor-model', dest='story_editor_model',
                        help='LLM model to use for StoryEditorAgent (defaults to --model)')
    parser.add_argument('--story-editor-provider', dest='story_editor_provider', choices=['openai', 'openrouter'],
                        help='LLM provider to use for StoryEditorAgent (defaults to --provider)')

    parser.add_argument('--character-consistency-model', dest='character_consistency_model',
                        help='LLM model to use for CharacterConsistencyAgent (defaults to --model)')
    parser.add_argument('--character-consistency-provider', dest='character_consistency_provider', choices=['openai', 'openrouter'],
                        help='LLM provider to use for CharacterConsistencyAgent (defaults to --provider)')

    # Debug and logging arguments
    parser.add_argument('--verbose', action='store_true', default=False,
                        help='Enable verbose output from CrewAI agents (default: False)')
    parser.add_argument('--interactive-editing', action='store_true', default=True,
                        help='Enable interactive story editing workflow (default: True)')

    # Video output options
    parser.add_argument('--create-fast-version', action='store_true', default=True,
                        help='Create an additional 2x speed video output for quick review (default: True)')

    # Continuation options
    parser.add_argument('--continue', dest='continue_generation', nargs='?', const=True, default=False,
                        help='Continue video generation from where it left off. Can be used with a path to a specific story directory.')

    args = parser.parse_args()

    # Skip validation if using --continue flag
    if not args.continue_generation:
        # Validate that title is provided when not using --continue
        if not args.title:
            parser.error('--title is required when not using --continue')

        # Validate that context is provided for fictional stories
        if args.story_type == 'fictional' and not args.context:
            parser.error('--context is required when story_type is "fictional"')

    return args


def get_agent_config(args, agent_name: str, global_model: str, global_provider: str) -> Tuple[str, str]:
    """
    Get agent-specific model and provider configuration with fallback to global values.

    Args:
        args: Parsed command line arguments
        agent_name: Name of the agent (e.g., 'researcher', 'editor', etc.)
        global_model: Global model to use as fallback
        global_provider: Global provider to use as fallback

    Returns:
        Tuple[str, str]: (model, provider) for the specific agent
    """
    # Convert agent name to attribute format (e.g., 'researcher' -> 'researcher_model')
    model_attr = f"{agent_name}_model"
    provider_attr = f"{agent_name}_provider"

    # Get agent-specific values or fall back to global values
    agent_model = getattr(args, model_attr, None) or global_model
    agent_provider = getattr(args, provider_attr, None) or global_provider

    return agent_model, agent_provider


def get_latest_story_dir() -> Optional[str]:
    """
    Get the most recently created story directory.

    Returns:
        Optional[str]: Path to the most recent story directory, or None if no directories exist.
    """
    assets_dir = 'assets'
    if not os.path.exists(assets_dir):
        return None

    # Get all subdirectories in the assets directory
    subdirs = [d for d in os.listdir(assets_dir) if os.path.isdir(os.path.join(assets_dir, d))]
    if not subdirs:
        return None

    # Sort by creation time (newest first)
    subdirs.sort(key=lambda d: os.path.getctime(os.path.join(assets_dir, d)), reverse=True)

    # Return the newest directory
    return os.path.join(assets_dir, subdirs[0])


def get_existing_audio_files(story_dir: str) -> List[str]:
    """
    Get a list of existing audio files in the story directory.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        List[str]: List of audio file paths
    """
    audio_dir = os.path.join(story_dir, 'audio')
    if not os.path.exists(audio_dir):
        return []

    # Get all MP3 files in the audio directory
    audio_files = glob.glob(os.path.join(audio_dir, '*.mp3'))

    # Sort the files by scene and segment number
    def extract_numbers(path):
        # Extract scene and segment numbers from the filename
        match = re.search(r'scene_(\d+)_segment_(\d+)', path)
        if match:
            return (int(match.group(1)), int(match.group(2)))
        return (0, 0)

    audio_files.sort(key=extract_numbers)
    return audio_files


def get_existing_image_files(story_dir: str) -> List[str]:
    """
    Get a list of existing image files in the story directory.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        List[str]: List of image file paths
    """
    image_dir = os.path.join(story_dir, 'images')
    if not os.path.exists(image_dir):
        return []

    # Get all PNG files in the images directory
    image_files = glob.glob(os.path.join(image_dir, '*.png'))

    # Sort the files by scene and segment number
    def extract_numbers(path):
        # Extract scene and segment numbers from the filename
        match = re.search(r'scene_(\d+)_segment_(\d+)', path)
        if match:
            return (int(match.group(1)), int(match.group(2)))
        return (0, 0)

    image_files.sort(key=extract_numbers)
    return image_files


def validate_story_directory(story_dir: str) -> Optional[str]:
    """
    Validate that a story directory can be used for continuation.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        Optional[str]: Validated story directory path or None if invalid
    """
    # Check if the directory exists
    if not os.path.exists(story_dir):
        logger.error(f"Directory does not exist: {story_dir}")
        return None

    # Check if story.json exists
    story_json_path = os.path.join(story_dir, 'story.json')
    if not os.path.exists(story_json_path):
        logger.error(f"No story.json found in the specified directory: {story_dir}")
        return None

    # Check if final_video.mp4 already exists
    final_video_path = os.path.join(story_dir, 'final_video.mp4')
    if os.path.exists(final_video_path):
        logger.error(f"Final video already exists in the directory: {story_dir}")
        logger.error("Cannot continue generation for a completed story.")
        return None

    # Create subdirectories if they don't exist
    os.makedirs(os.path.join(story_dir, 'audio'), exist_ok=True)
    os.makedirs(os.path.join(story_dir, 'images'), exist_ok=True)

    return story_dir


def create_story_directory(title: str) -> str:
    """
    Create a unique directory for the story assets.

    Args:
        title (str): The title of the story to create a directory for.

    Returns:
        str: Path to the created story directory.
    """
    # Create a timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create a safe directory name from the title
    safe_title = re.sub(r'[^\w\s-]', '', title).strip().lower()
    safe_title = re.sub(r'[-\s]+', '_', safe_title)

    # Combine title and timestamp
    dir_name = f"{safe_title}_{timestamp}"

    # Create the full path
    story_dir = os.path.join('assets', dir_name)

    # Create the directory and subdirectories
    os.makedirs(story_dir, exist_ok=True)
    os.makedirs(os.path.join(story_dir, 'audio'), exist_ok=True)
    os.makedirs(os.path.join(story_dir, 'images'), exist_ok=True)

    logger.info(f"Created story directory: {story_dir}")
    return story_dir


def main() -> Optional[Tuple[str, str]] | str:
    """
    Main execution function for the AI Hindi Story Video Generator.

    This function orchestrates the entire video generation pipeline including:
    - Environment setup and argument parsing
    - Story research and generation
    - Character consistency analysis
    - Audio and image generation
    - Video assembly

    Returns:
        Optional[Tuple[str, str]] | str: Either a tuple of (normal_video_path, fast_video_path)
        if fast version is enabled, or just the normal video path string. Returns None on error.
    """
    # Parse arguments
    args = parse_arguments()

    # Extract core arguments for environment setup
    llm_provider = args.provider
    dev_mode = args.dev
    image_provider = args.image_provider

    # Setup environment with all relevant providers and dev mode
    setup_environment(llm_provider=llm_provider, dev_mode=dev_mode, image_provider=image_provider)

    # Extract other arguments
    title = args.title
    context = args.context or ""
    story_type = args.story_type
    genre = args.genre
    references = args.references or []
    image_style = args.image_style
    image_generation_enabled = args.image_generation
    image_model = args.image_model
    max_concurrent_requests = args.max_concurrent_requests
    use_parallel_image_generation = args.use_parallel_image_generation
    elevenlabs_voice_id = args.elevenlabs_voice_id
    verbose = args.verbose
    interactive_editing = args.interactive_editing
    model = args.model
    max_tokens_per_minute = args.max_tokens_per_minute
    create_fast_version = args.create_fast_version
    continue_generation = args.continue_generation

    # Handle the continue flag
    story_dir = None

    if continue_generation:
        # If a path is provided with --continue
        if isinstance(continue_generation, str):
            # Use the provided path
            story_dir = continue_generation
            logger.info(f"Attempting to continue generation from specified directory: {story_dir}")

        else:
            # Find the most recent story directory
            story_dir = get_latest_story_dir()
            if story_dir:
                logger.info(f"Attempting to continue generation from most recent directory: {story_dir}")
            else:
                logger.error("No existing story directories found to continue from.")
                return None

        # Validate the story directory
        story_dir = validate_story_directory(story_dir)

        if not story_dir:
            logger.error("Cannot continue generation. Exiting.")
            return None

        # Log that we're continuing from an existing directory
        logger.info(f"Continuing video generation from: {story_dir}")

    else:
        # Log normal generation parameters
        logger.info(f"Starting {story_type} story generation for title: '{title}' with genre: {genre}")

        if references:
            logger.info(f"Using {len(references)} reference URLs for research")

        logger.info(f"Image style set to: {image_style}")
        logger.info(f"TTS Provider: {'Coqui-AI (local)' if args.dev else f'ElevenLabs (voice ID: {elevenlabs_voice_id})'}")
        logger.info(f"Using LLM provider: {llm_provider}")
        logger.info(f"Using LLM model: {model}")
        logger.info(f"CrewAI verbose mode: {'Enabled' if verbose else 'Disabled'}")
        logger.info(f"Interactive editing mode: {'Enabled' if interactive_editing else 'Disabled'}")
        logger.info(f"Image generation enabled: {'Enabled' if image_generation_enabled else 'Disabled'}")

        if image_generation_enabled:
            logger.info(f"Using image provider: {image_provider}")
            logger.info(f"Using image model: {image_model}")

        logger.info(f"Parallel image generation: {'Enabled' if use_parallel_image_generation else 'Disabled'}")

        if use_parallel_image_generation:
            logger.info(f"Maximum concurrent requests: {max_concurrent_requests}")

        logger.info(f"2x speed video creation: {'Enabled' if create_fast_version else 'Disabled'}")

        # Create a new directory for this story
        story_dir = create_story_directory(title)

    # Define a path for story assets
    story_json_path = os.path.join(story_dir, 'story.json')

    # Check if a story already exists in the new directory
    story: Optional[Story] = None
    if os.path.exists(story_json_path):
        logger.info(f"Using existing story from {story_json_path}")

        try:
            with open(story_json_path, 'r', encoding='utf-8') as f:
                story_data = json.load(f)
                story = Story(**story_data)

        except Exception as e:
            logger.error(f"Error reading existing story: {str(e)}")
            logger.info("Generating new story...")
            story = None

    if story is None:
        # Step 1: Research using the unified ResearcherAgent
        logger.info(f"Using ResearcherAgent for {story_type} story research")

        # Get agent-specific configuration
        researcher_model, researcher_provider = get_agent_config(args, 'researcher', model, llm_provider)
        logger.info(f"ResearcherAgent using model: {researcher_model}, provider: {researcher_provider}")

        researcher = create_rate_limited_agent(
            role="Researcher",
            goal=f"Research {story_type} story elements for a Hindi {genre} story",
            backstory="You are a skilled researcher who gathers information for compelling stories.",
            model=researcher_model,
            max_tokens_per_minute=max_tokens_per_minute,
            verbose=verbose,
            provider=researcher_provider
        )

        research_agent = ResearcherAgent(verbose=verbose, model=researcher_model, provider=researcher_provider)
        research_agent.llm = researcher.llm  # Use the rate-limited LLM
        research_data = research_agent.research(title, context, story_type, references)

        # Step 2: Edit and enhance research
        # Get agent-specific configuration
        editor_model, editor_provider = get_agent_config(args, 'editor', model, llm_provider)
        logger.info(f"EditorAgent using model: {editor_model}, provider: {editor_provider}")

        editor = create_rate_limited_agent(
            role="Editor",
            goal="Enhance research data with narrative elements",
            backstory="You are an editor who enhances research with narrative structure and thematic elements.",
            model=editor_model,
            max_tokens_per_minute=max_tokens_per_minute,
            verbose=verbose,
            provider=editor_provider
        )

        editor_agent = EditorAgent(verbose=verbose, model=editor_model, provider=editor_provider)
        editor_agent.llm = editor.llm  # Use the rate-limited LLM
        enhanced_research = editor_agent.enhance(research_data, story_type, genre)

        # Step 3: Write story
        # Get agent-specific configuration
        writer_model, writer_provider = get_agent_config(args, 'writer', model, llm_provider)
        logger.info(f"WriterAgent using model: {writer_model}, provider: {writer_provider}")

        writer = create_rate_limited_agent(
            role="Writer",
            goal=f"Write a compelling Hindi {genre} story",
            backstory="You are a talented Hindi writer who creates engaging stories.",
            model=writer_model,
            max_tokens_per_minute=max_tokens_per_minute,
            verbose=verbose,
            provider=writer_provider
        )

        writer_agent = WriterAgent(verbose=verbose, model=writer_model, provider=writer_provider)
        writer_agent.llm = writer.llm  # Use the rate-limited LLM
        story = writer_agent.write_story(enhanced_research, title, story_type, genre)

        # Save story to JSON in the new directory
        with open(story_json_path, 'w', encoding='utf-8') as f:
            json.dump(story.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"Story written and saved to {story_json_path}")

    # Log the story structure
    logger.info(f"Story has {len(story.scenes)} scenes")

    # Step 3.5: Interactive story editing (if enabled)
    if interactive_editing:
        logger.info("Starting interactive story editing workflow")

        # Get agent-specific configuration
        story_editor_model, story_editor_provider = get_agent_config(args, 'story_editor', model, llm_provider)
        logger.info(f"StoryEditorAgent using model: {story_editor_model}, provider: {story_editor_provider}")

        editor = create_rate_limited_agent(
            role="Story Editor",
            goal="Edit and refine the story based on user feedback",
            backstory="You are an expert story editor who helps refine and improve stories.",
            model=story_editor_model,
            max_tokens_per_minute=max_tokens_per_minute,
            verbose=verbose,
            provider=story_editor_provider
        )

        story_editor = StoryEditorAgent(verbose=verbose, model=story_editor_model, provider=story_editor_provider)
        story_editor.llm = editor.llm  # Use the rate-limited LLM

        # Pass the story_json_path to enable real-time updates during editing
        story = story_editor.interactive_edit(story, story_json_path)

        # Final save is still needed to ensure consistency
        with open(story_json_path, 'w', encoding='utf-8') as f:
            json.dump(story.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"Edited story saved to {story_json_path}")
        print("Interactive editing complete. Proceeding to next steps...")

    # Step 4: Split dialogue for scenes
    # Get agent-specific configuration
    dialogue_splitter_model, dialogue_splitter_provider = get_agent_config(args, 'dialogue_splitter', model, llm_provider)
    logger.info(f"DialogueSplitterAgent using model: {dialogue_splitter_model}, provider: {dialogue_splitter_provider}")

    splitter = create_rate_limited_agent(
        role="Dialogue Splitter",
        goal="Split Hindi narration into optimal segments for TTS and image generation",
        backstory="You are an expert in audio-visual storytelling who optimizes content for narration and visuals.",
        model=dialogue_splitter_model,
        max_tokens_per_minute=max_tokens_per_minute,
        verbose=verbose,
        provider=dialogue_splitter_provider
    )

    dialogue_splitter = DialogueSplitterAgent(verbose=verbose, model=dialogue_splitter_model, provider=dialogue_splitter_provider)
    dialogue_splitter.llm = splitter.llm  # Use the rate-limited LLM
    scenes = dialogue_splitter.split(story)

    # Step 4.5: Character Consistency Analysis and Reference Generation
    # Check if character consistency data already exists
    character_data_path = os.path.join(story_dir, 'character_consistency.json')
    character_data = None

    if continue_generation and os.path.exists(character_data_path):
        logger.info("Found existing character consistency data, loading...")

        try:
            with open(character_data_path, 'r', encoding='utf-8') as f:
                character_data = CharacterConsistencyData(**json.load(f))

            logger.info(f"Loaded character data for {len(character_data.characters)} characters")

        except Exception as e:
            logger.warning(f"Failed to load existing character data: {e}. Regenerating...")
            character_data = None

    if character_data is None:
        # Get agent-specific configuration for character consistency
        character_consistency_model, character_consistency_provider = get_agent_config(args, 'character_consistency', model, llm_provider)
        logger.info(f"CharacterConsistencyAgent using model: {character_consistency_model}, provider: {character_consistency_provider}")

        # Create character consistency agent
        char_agent = create_rate_limited_agent(
            role="Character Consistency Analyst",
            goal="Analyze story characters and generate reference images for visual consistency",
            backstory="You are an expert in character design and visual storytelling who ensures character consistency.",
            model=character_consistency_model,
            max_tokens_per_minute=max_tokens_per_minute,
            verbose=verbose,
            provider=character_consistency_provider
        )

        character_consistency_agent = CharacterConsistencyAgent(
            verbose=verbose,
            model=character_consistency_model,
            provider=character_consistency_provider,
            image_model=image_model,
            image_provider=image_provider
        )
        character_consistency_agent.llm = char_agent.llm  # Use the rate-limited LLM

        # Process story for character consistency
        character_data = character_consistency_agent.process_story_for_consistency(
            story, story_dir, image_style
        )

        logger.info(f"Character consistency analysis complete. Found {len(character_data.characters)} characters")
        print(f"Character analysis complete. Found {len(character_data.characters)} characters.")

    else:
        print(f"Using existing character data for {len(character_data.characters)} characters.")

    # Step 5: Generate image descriptions with character consistency
    # Get agent-specific configuration
    image_desc_gen_model, image_desc_gen_provider = get_agent_config(args, 'image_desc_gen', model, llm_provider)
    logger.info(f"ImageDescriptionGenerator using model: {image_desc_gen_model}, provider: {image_desc_gen_provider}")

    desc_gen = create_rate_limited_agent(
        role="Image Description Generator",
        goal="Create detailed image prompts from scene segments with character consistency",
        backstory="You are an expert in visual storytelling who creates compelling image descriptions with character consistency.",
        model=image_desc_gen_model,
        max_tokens_per_minute=max_tokens_per_minute,
        verbose=verbose,
        provider=image_desc_gen_provider
    )
    image_desc_gen = ImageDescriptionGenerator(image_generation_model_name=image_model,
                                               verbose=verbose,
                                               model=image_desc_gen_model,
                                               provider=image_desc_gen_provider)
    image_desc_gen.llm = desc_gen.llm  # Use the rate-limited LLM

    # Generate image descriptions with character consistency if characters were found
    if character_data and character_data.characters:
        logger.info("Generating image descriptions with character consistency")
        image_descriptions = image_desc_gen.generate_with_character_consistency(scenes, character_data, image_style)

    else:
        logger.info("No characters found, generating standard image descriptions")
        image_descriptions = image_desc_gen.generate(scenes, image_style)

    # Step 6: Generate TTS narration
    # Check for existing audio files if continuing
    existing_audio_files = []

    if continue_generation:
        existing_audio_files = get_existing_audio_files(story_dir)

        if existing_audio_files:
            logger.info(f"Found {len(existing_audio_files)} existing audio files")

    # Only generate audio for scenes that don't have audio files yet
    if len(existing_audio_files) < len(scenes):
        logger.info(f"Generating audio for {len(scenes) - len(existing_audio_files)} remaining scenes")

        # Use the TTS generator factory to select the appropriate generator based on dev mode
        dev_mode = args.dev
        tts_generator = create_tts_generator(
            dev_mode=dev_mode,
            voice_id=elevenlabs_voice_id
        )

        # Log which TTS provider is being used
        if dev_mode:
            logger.info("Using espeak-ng TTS for local development (--dev=True)")
        else:
            logger.info(f"Using ElevenLabs TTS with voice ID: {elevenlabs_voice_id} (--dev=False)")

        # If continuing, we need to determine which scenes need audio generation
        if continue_generation and existing_audio_files:
            # Get the highest scene and segment numbers from existing files
            max_scene = 0
            max_segment = 0

            for audio_file in existing_audio_files:
                match = re.search(r'scene_(\d+)_segment_(\d+)', audio_file)
                if match:
                    scene_num = int(match.group(1))
                    segment_num = int(match.group(2))
                    if scene_num > max_scene or (scene_num == max_scene and segment_num > max_segment):
                        max_scene = scene_num
                        max_segment = segment_num

            # Filter scenes to only include those that need generation
            remaining_scenes = [
                scene for scene in scenes
                if scene.scene_number > max_scene or
                (scene.scene_number == max_scene and scene.segment_number > max_segment)
            ]

            # Generate audio for remaining scenes
            new_audio_paths = tts_generator.generate_audio(remaining_scenes, os.path.join(story_dir, 'audio'))
            audio_paths = existing_audio_files + new_audio_paths

        else:
            # Generate audio for all scenes
            audio_paths = tts_generator.generate_audio(scenes, os.path.join(story_dir, 'audio'))

    else:
        logger.info("All audio files already exist, skipping audio generation")
        audio_paths = existing_audio_files

    # Step 7: Generate images (only if image generation is enabled)
    image_paths: List[str] = []

    if image_generation_enabled:
        # Check for existing image files if continuing
        existing_image_files: List[str] = []

        if continue_generation:
            existing_image_files = get_existing_image_files(story_dir)

            if existing_image_files:
                logger.info(f"Found {len(existing_image_files)} existing image files")

        # Create the image generator
        image_generator = ImageGenerator(
            model=image_model,
            provider=image_provider,
            max_concurrent_requests=max_concurrent_requests,
            use_parallel=use_parallel_image_generation
        )

        # Initialize new_image_paths to prevent UnboundLocalError
        new_image_paths: List[str] = []

        # Only generate images for scenes that don't have image files yet
        if len(existing_image_files) < len(image_descriptions):
            logger.info(
                f"Generating images for {len(image_descriptions) - len(existing_image_files)} remaining scenes"
            )

            # If continuing and we have existing image files, determine which scenes need image generation
            if continue_generation and existing_image_files:
                # Get the highest scene and segment numbers from existing files
                max_scene = 0
                max_segment = 0

                for image_file in existing_image_files:
                    match = re.search(r'scene_(\d+)_segment_(\d+)', image_file)

                    if match:
                        scene_num = int(match.group(1))
                        segment_num = int(match.group(2))

                        if scene_num > max_scene or (scene_num == max_scene and segment_num > max_segment):
                            max_scene = scene_num
                            max_segment = segment_num

                # Filter image descriptions to only include those that need generation
                remaining_image_descriptions = [
                    desc for desc in image_descriptions
                    if desc.scene_number > max_scene or
                    (desc.scene_number == max_scene and desc.segment_number > max_segment)
                ]

                # Generate images for remaining scenes only if there are any
                if remaining_image_descriptions:
                    # Check if we have enhanced image prompts with character consistency
                    if hasattr(remaining_image_descriptions[0], 'characters_in_scene'):
                        logger.info("Using character-consistent image generation for remaining scenes")
                        new_image_paths = image_generator.generate_images_with_character_consistency(
                            remaining_image_descriptions,
                            os.path.join(story_dir, 'images'),
                            width=1920,
                            height=1080,
                            image_style=image_style,
                            group_image_path=character_data.characters_group_image_path
                        )

                    else:
                        new_image_paths = image_generator.generate_images(
                            remaining_image_descriptions,
                            os.path.join(story_dir, 'images'),
                            width=1920,
                            height=1080,
                            image_style=image_style,
                            reference_image_path=character_data.characters_group_image_path
                        )
                else:
                    logger.info("No remaining scenes to generate images for")
                    new_image_paths = []

            else:
                # Either not continuing, or continuing but no existing image files - generate all images
                if image_descriptions:
                    # Check if we have enhanced image prompts with character consistency
                    if hasattr(image_descriptions[0], 'characters_in_scene'):
                        logger.info("Using character-consistent image generation for all scenes")
                        new_image_paths = image_generator.generate_images_with_character_consistency(
                            image_descriptions,
                            os.path.join(story_dir, 'images'),
                            width=1920,
                            height=1080,
                            image_style=image_style,
                            group_image_path=character_data.characters_group_image_path
                        )

                    else:
                        new_image_paths = image_generator.generate_images(
                            image_descriptions,
                            os.path.join(story_dir, 'images'),
                            width=1920,
                            height=1080,
                            image_style=image_style,
                            reference_image_path=character_data.characters_group_image_path
                        )
                else:
                    logger.warning("No image descriptions available for generation")
                    new_image_paths = []

            image_paths = existing_image_files + new_image_paths

        else:
            logger.info("All image files already exist, skipping image generation")
            image_paths = existing_image_files

    else:
        logger.info("Image generation is disabled. Skipping image generation steps.")

    # Log all generated files
    logger.info(f"Generated audio files: {audio_paths}")
    logger.info(f"Generated image files: {image_paths}")

    # Ensure audio_paths and image_paths match up
    if len(audio_paths) != len(image_paths):
        logger.warning(f"Mismatch between audio files ({len(audio_paths)}) and image files ({len(image_paths)})")

        # Use the minimum number of files
        min_count = min(len(audio_paths), len(image_paths))
        audio_paths = audio_paths[:min_count]
        image_paths = image_paths[:min_count]

        logger.info(f"Using {min_count} matched pairs of audio and image files")

    # Step 8: Assemble video
    video_assembler = VideoAssembler(create_fast_version=create_fast_version)
    result = video_assembler.assemble(audio_paths, image_paths, os.path.join(story_dir, 'final_video.mp4'))

    # Handle the return value which could be a single path or a tuple of paths
    if isinstance(result, Tuple):
        final_video_path, fast_video_path = result
        logger.info(f"Video generation complete. Final video saved to: {final_video_path}")
        logger.info(f"2x speed version saved to: {fast_video_path}")
        return (final_video_path, fast_video_path)

    else:
        final_video_path = result
        logger.info(f"Video generation complete. Final video saved to: {final_video_path}")
        return final_video_path

if __name__ == "__main__":
    try:
        result = main()

        if isinstance(result, Tuple):
            final_video_path, fast_video_path = result
            print(f"\nSuccess! Final video saved to: {final_video_path}")
            print(f"2x speed version saved to: {fast_video_path}")

        else:
            final_video_path = result
            print(f"\nSuccess! Final video saved to: {final_video_path}")

    except Exception as e:
        logger.error(f"Error during execution: {str(e)}", exc_info=True)
        print(f"\nError: {str(e)}")
