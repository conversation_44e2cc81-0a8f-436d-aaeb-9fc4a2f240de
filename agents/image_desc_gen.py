"""
Image Description Generator
-------------------------
Converts each dialogue/cue into a detailed image prompt.
"""
import os
import logging
from typing import List

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process

from models.schema import SceneSegmentList, ImagePrompt, EnhancedImagePrompt, CharacterConsistencyData
from utils.content_moderation import sanitize_prompt

logger = logging.getLogger(__name__)


class ImageDescriptionGenerator:
    def __init__(self,
                 image_generation_model_name: str = "black-forest-labs/FLUX-1.1-pro",
                 verbose: bool = False,
                 model: str = "gpt-4o-mini",
                 provider: str = "openai"):
        """
        Initialize the image description generator with necessary API keys.

        Args:
            image_generation_model_name (str): The name of the image generation model to use.
                Defaults to "black-forest-labs/FLUX-1.1-pro".
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.verbose = verbose
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.llm_model = model
        self.provider = provider

        # Use provided image_generation_model_name
        self.image_generation_model_name = image_generation_model_name

        # Extract the short model name for prompts (remove organization prefix if present)
        if "/" in self.image_generation_model_name:
            self.image_generation_short_model_name = self.image_generation_model_name.split("/")[-1]
        else:
            self.image_generation_short_model_name = self.image_generation_model_name

        if not self.openai_api_key:
            raise ValueError("Missing required API key for ImageDescriptionGenerator")

        # Initialize the LLM
        self.llm = ChatOpenAI(
            model=self.llm_model,
            temperature=0.5,
            api_key=self.openai_api_key
        )

    def generate(self, scene_segments: SceneSegmentList, image_style: str = 'realistic') -> List[ImagePrompt]:
        """
        Generate detailed image descriptions for each scene segment.

        Args:
            scene_segments (SceneSegmentList): List of scene segments with narration and visual cues
            image_style (str): Visual aesthetic style for generated images - realistic, anime, cartoon, etc.

        Returns:
            List[ImagePrompt]: List of scene segments with added image prompts optimized for the specified model
        """
        logger.info(f"Starting image description generation with style: {image_style}")

        # Create the image description generator agent
        desc_generator = Agent(
            role=f"{image_style.capitalize()} Visual Prompt Engineer",
            goal=f"Create detailed {image_style} style image prompts for a Hindi story using content-safe, cinematic language",
            backstory=f"""You are an expert in visual storytelling and prompt engineering, specializing in {image_style} style imagery.
            Your task is to create detailed image prompts in {image_style} style that can be used with an image
            generation model to create compelling visuals for a Hindi story.

            CONTENT GUIDELINES - ALWAYS FOLLOW THESE RULES:
            • Use cinematic and artistic terminology instead of explicit descriptions
            • Focus on emotional atmosphere, lighting, and composition rather than graphic details
            • Describe action through body language, facial expressions, and environmental storytelling
            • Use professional film/photography terms: "dramatic lighting", "tense atmosphere", "dynamic composition"
            • Avoid explicit violence terms - instead use: "intense confrontation", "dramatic tension", "high-stakes situation"
            • For conflict scenes, emphasize: shadows, dramatic angles, environmental storytelling, character emotions
            • Use "show don't tell" principles - convey story through visual metaphors and symbolism

            PREFERRED LANGUAGE PATTERNS:
            • Instead of "blood" → "dramatic red lighting", "crimson shadows", "intense red tones"
            • Instead of "violence" → "dramatic confrontation", "intense standoff", "high-tension scene"
            • Instead of "weapons" → "metallic objects", "tactical equipment", "professional gear"
            • Instead of "injury" → "character under pressure", "dramatic expression", "intense emotion"

            Your prompts should create powerful, emotionally impactful images while using safe, artistic language.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        enhanced_segments = []

        # Process each segment individually
        for i, segment in enumerate(scene_segments):
            logger.info(f"Generating image description for segment {i+1}/{len(scene_segments)}")

            # Create the image description task
            desc_task = Task(
                description=f"""
                Create a detailed image prompt in {image_style} style for the following scene segment:

                Scene Number: {segment.scene_number}
                Segment Number: {segment.segment_number}
                Narration: {segment.narration}
                Visual Cue: {segment.visual_cue}
                Style: {image_style}

                CONTENT-SAFE PROMPT CREATION GUIDELINES:

                1. CINEMATIC APPROACH: Describe scenes like a professional cinematographer
                   - Use film terminology: "dramatic close-up", "wide establishing shot", "dynamic angle"
                   - Focus on composition, lighting, and visual storytelling
                   - Emphasize mood and atmosphere over explicit content

                2. EMOTIONAL STORYTELLING: Convey tension and drama through:
                   - Character expressions and body language
                   - Environmental details and symbolism
                   - Lighting and shadow play
                   - Color psychology and visual metaphors

                3. SAFE LANGUAGE SUBSTITUTIONS:
                   - Action scenes → "intense dramatic moment", "high-stakes confrontation"
                   - Conflict → "tense standoff", "dramatic confrontation", "pivotal encounter"
                   - Physical elements → "dramatic red lighting", "metallic objects", "tactical equipment"
                   - Emotional states → "character under intense pressure", "determined expression"

                4. TECHNICAL REQUIREMENTS:
                   - Include specific {image_style} style characteristics
                   - Use professional photography/film terms
                   - Focus on visual composition and artistic elements
                   - Ensure compatibility with {self.image_generation_short_model_name} model

                5. EXAMPLES OF PREFERRED DESCRIPTIONS:
                   - "Dramatic silhouette against moody lighting with intense shadows"
                   - "Character with determined expression in high-contrast cinematography"
                   - "Tense atmosphere with dynamic composition and dramatic angles"
                   - "Professional setting with metallic equipment and focused lighting"

                Create a 100-150 word prompt that captures the scene's emotional impact using cinematic, artistic language.
                """,
                agent=desc_generator,
                expected_output=f"""
                A detailed, content-safe image prompt in {image_style} style using cinematic language that can be used with the {self.image_generation_short_model_name} model.
                """,
                llm=self.llm
            )

            # Create and run the crew for this segment
            crew = Crew(
                process=Process.sequential,
                tasks=[desc_task],
                agents=[desc_generator],
                manager_llm=self.llm,
                verbose=self.verbose,
            )

            crew_output = crew.kickoff()
            result = crew_output.raw

            # Apply content moderation to the generated image prompt
            sanitized_prompt = sanitize_prompt(result.strip())

            # Create an ImagePrompt object
            image_prompt = ImagePrompt(
                scene_number=segment.scene_number,
                segment_number=segment.segment_number,
                narration=segment.narration,
                visual_cue=segment.visual_cue,
                image_prompt=sanitized_prompt,
                estimated_duration_seconds=segment.estimated_duration_seconds
            )
            enhanced_segments.append(image_prompt)

        # Log detailed information about the enhanced segments
        logger.info("Image description generation completed successfully")
        logger.info(f"Generated {len(enhanced_segments)} image descriptions")

        for i, segment in enumerate(enhanced_segments):
            logger.info(f"Image description {i+1}: Scene {segment.scene_number}, Segment {segment.segment_number}")
            # Log a preview of the image prompt (first 50 characters)
            prompt_preview = segment.image_prompt[:50] + '...' if segment.image_prompt else 'No prompt'
            logger.info(f"Image prompt preview: {prompt_preview}")

        return enhanced_segments

    def generate_with_character_consistency(self,
                                          scene_segments: SceneSegmentList,
                                          character_data: CharacterConsistencyData,
                                          image_style: str = 'realistic') -> List[EnhancedImagePrompt]:
        """
        Generate detailed image descriptions with character consistency.

        Args:
            scene_segments (SceneSegmentList): List of scene segments with narration and visual cues
            character_data (CharacterConsistencyData): Character consistency data with references
            image_style (str): Visual aesthetic style for generated images

        Returns:
            List[EnhancedImagePrompt]: List of enhanced image prompts with character consistency
        """
        logger.info(f"Starting character-consistent image description generation with style: {image_style}")

        # Create character reference mapping
        character_refs = {ref.character_name: ref for ref in character_data.character_references}
        character_profiles = {char.name: char for char in character_data.characters}

        # Create the enhanced image description generator agent
        desc_generator = Agent(
            role=f"Character-Consistent {image_style.capitalize()} Visual Prompt Engineer",
            goal=f"Create detailed {image_style} style image prompts with character consistency using content-safe, cinematic language",
            backstory=f"""You are an expert in visual storytelling and prompt engineering, specializing in {image_style} style imagery
            with character consistency. Your task is to create detailed image prompts that maintain visual consistency for characters
            across different scenes while creating compelling {image_style} style visuals for a Hindi story.

            CONTENT GUIDELINES FOR CHARACTER-CONSISTENT PROMPTS:
            • Maintain character visual consistency while using safe, professional language
            • Focus on distinctive visual features, clothing, and character expressions
            • Use cinematic terminology for action and dramatic scenes
            • Emphasize character emotions, body language, and environmental storytelling
            • Describe character interactions through positioning, expressions, and atmosphere

            CHARACTER DESCRIPTION PRINCIPLES:
            • Professional archetype language: "protagonist", "antagonist", "supporting character"
            • Visual consistency focus: facial features, clothing style, distinctive characteristics
            • Emotional storytelling: character expressions, posture, and presence in scene
            • Cinematic framing: how characters are positioned and lit within the composition

            SAFE LANGUAGE FOR CHARACTER SCENES:
            • Character conflicts → "tense character dynamics", "dramatic confrontation between characters"
            • Character states → "character under pressure", "determined expression", "intense focus"
            • Character interactions → "pivotal character moment", "dramatic character encounter"
            • Character positioning → "dynamic character composition", "strategic character placement"

            Always maintain character consistency while using artistic, content-safe language.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        enhanced_segments = []

        # Process each segment individually
        for i, segment in enumerate(scene_segments):
            logger.info(f"Generating character-consistent image description for segment {i+1}/{len(scene_segments)}")

            # Identify characters in this scene
            characters_in_scene = self._identify_characters_in_scene(segment, character_profiles)

            # Get character descriptions and reference info
            character_descriptions = {}
            character_reference_paths = {}

            for char_name in characters_in_scene:
                if char_name in character_profiles:
                    char = character_profiles[char_name]
                    character_descriptions[char_name] = f"{char.physical_description}. {char.clothing_style}. {char.distinctive_features}"

                    if char_name in character_refs:
                        character_reference_paths[char_name] = character_refs[char_name].reference_image_path

            # Create character consistency context for flux-kontext-pro
            character_context = ""
            if characters_in_scene:
                character_context = "\n\nCHARACTER REFERENCE REQUIREMENTS FOR FLUX-KONTEXT-PRO:\n"
                character_names_list = ", ".join(characters_in_scene)
                character_context += f"- Characters in scene: {character_names_list}\n"
                character_context += f"- Reference these characters by their labeled names from the provided reference image\n"
                character_context += f"- Maintain exact visual consistency with the labeled reference image\n"
                character_context += f"- Focus on scene composition while preserving character appearance from reference\n"

            # Create the enhanced image description task
            desc_task = Task(
                description=f"""
                Create a detailed image prompt in {image_style} style for the following scene segment with character consistency:

                Scene Number: {segment.scene_number}
                Segment Number: {segment.segment_number}
                Narration: {segment.narration}
                Visual Cue: {segment.visual_cue}
                Style: {image_style}
                Characters in Scene: {', '.join(characters_in_scene) if characters_in_scene else 'None identified'}
                {character_context}

                CHARACTER-CONSISTENT CONTENT-SAFE PROMPT GUIDELINES:

                1. CHARACTER CONSISTENCY REQUIREMENTS:
                   - Maintain exact visual descriptions for each character as specified above
                   - Ensure character features, clothing, and distinctive elements match previous scenes
                   - Use professional character archetype language (protagonist, antagonist, etc.)
                   - Focus on character expressions, posture, and emotional states

                2. CINEMATIC CHARACTER STORYTELLING:
                   - Describe character positioning using film composition terms
                   - Emphasize character emotions through facial expressions and body language
                   - Use lighting and shadows to enhance character presence and mood
                   - Create visual hierarchy through character placement and framing

                3. CONTENT-SAFE CHARACTER INTERACTIONS:
                   - Character conflicts → "tense character dynamics", "dramatic standoff between characters"
                   - Character emotions → "intense character focus", "determined character expression"
                   - Character actions → "character in motion", "dynamic character positioning"
                   - Character relationships → "pivotal character encounter", "significant character moment"

                4. TECHNICAL CHARACTER REQUIREMENTS:
                   - Maintain visual consistency with character reference descriptions
                   - Use {image_style} style characteristics for character rendering
                   - Ensure character descriptions work with {self.image_generation_short_model_name} model
                   - Balance character detail with overall scene composition

                5. FLUX-KONTEXT-PRO REFERENCE-BASED PATTERNS:
                   - "Scene showing [character names] from reference image in [setting]"
                   - "Characters [names] as shown in reference, positioned in [scene context]"
                   - "[Character names] maintaining reference appearance in [cinematic environment]"
                   - Focus on scene composition while referencing labeled characters by name

                Create a 150-200 word reference-based prompt optimized for flux-kontext-pro using cinematic, content-safe language.
                """,
                agent=desc_generator,
                expected_output=f"""
                A detailed reference-based image prompt in {image_style} style optimized for flux-kontext-pro character consistency, using content-safe, cinematic language that references characters by their labeled names from the reference image.
                """,
                llm=self.llm
            )

            # Create and run the crew for this segment
            crew = Crew(
                process=Process.sequential,
                tasks=[desc_task],
                agents=[desc_generator],
                manager_llm=self.llm,
                verbose=self.verbose,
            )

            crew_output = crew.kickoff()
            result = crew_output.raw

            # Apply content moderation to the generated image prompt
            sanitized_prompt = sanitize_prompt(result.strip())

            # Create an EnhancedImagePrompt object
            enhanced_prompt = EnhancedImagePrompt(
                scene_number=segment.scene_number,
                segment_number=segment.segment_number,
                narration=segment.narration,
                visual_cue=segment.visual_cue,
                image_prompt=sanitized_prompt,
                estimated_duration_seconds=segment.estimated_duration_seconds,
                characters_in_scene=characters_in_scene,
                character_descriptions=character_descriptions,
                character_reference_paths=character_reference_paths
            )
            enhanced_segments.append(enhanced_prompt)

        # Log detailed information about the enhanced segments
        logger.info("Character-consistent image description generation completed successfully")
        logger.info(f"Generated {len(enhanced_segments)} enhanced image descriptions")

        return enhanced_segments

    def _identify_characters_in_scene(self, segment, character_profiles) -> List[str]:
        """
        Identify which characters appear in a given scene segment.

        Args:
            segment: Scene segment to analyze
            character_profiles: Dictionary of character profiles

        Returns:
            List[str]: Names of characters identified in the scene
        """
        characters_found = []

        # Combine narration and visual cue for analysis
        scene_text = f"{segment.narration} {segment.visual_cue}".lower()

        # Check for each character by name
        for char_name, char_profile in character_profiles.items():
            # Check if character name appears in the scene
            if char_name.lower() in scene_text:
                characters_found.append(char_name)
                continue

            # Check if character appears in the scenes_appeared list
            if segment.scene_number in char_profile.scenes_appeared:
                characters_found.append(char_name)

        return characters_found
