"""
Character Consistency Agent
--------------------------
Analyzes stories to extract characters and generate reference images for visual consistency.
"""

import os
import json
import logging
import datetime
from typing import Optional

from crewai import Agent, Task, Crew, Process

from utils.image_generator import ImageGenerator
from utils.parsers import CharacterConsistencyDataParser
from utils.content_moderation import sanitize_character_description
from models.schema import Story, Character, CharacterReference, CharacterConsistencyData

logger = logging.getLogger(__name__)


class CharacterConsistencyAgent:
    """
    Agent for analyzing stories and generating character consistency data.

    This agent performs comprehensive character analysis on stories to extract
    character information and generate reference images for visual consistency
    across all generated scenes.
    """

    def __init__(self,
                 verbose: bool = False,
                 model: str = "gpt-4o-mini",
                 provider: str = "openai",
                 image_model: str = "black-forest-labs/FLUX-1.1-pro",
                 image_provider: str = "deepinfra") -> None:
        """
        Initialize the Character Consistency Agent.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI agents.
            model (str): LLM model to use for character analysis.
            provider (str): LLM provider to use for character analysis.
            image_model (str): Image generation model to use for reference images.
            image_provider (str): Image generation provider to use for reference images.
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider
        self.image_model = image_model
        self.image_provider = image_provider
        self.llm = None  # Will be set by the main script

    def analyze_characters(self, story: Story) -> CharacterConsistencyData:
        """
        Analyze the story to extract characters and their descriptions.

        This method uses a CrewAI agent to perform comprehensive character analysis,
        extracting detailed character profiles including physical descriptions,
        clothing styles, and distinctive features for visual consistency.

        Args:
            story (Story): The story to analyze for character extraction.

        Returns:
            CharacterConsistencyData: Character analysis results containing character
            profiles and metadata. Returns empty data if parsing fails.

        Raises:
            Exception: If character analysis fails due to LLM or parsing errors.
        """
        logger.info("Starting character analysis for story consistency")

        # Create a parser for the CharacterConsistencyData model
        parser = CharacterConsistencyDataParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the character analysis agent
        character_analyzer = Agent(
            role="Character Consistency Analyst",
            goal="Analyze the story to identify all characters and create detailed, content-safe visual profiles for consistency",
            backstory="""You are an expert in character design and visual storytelling. Your task is to carefully
            analyze the story and identify all characters that appear, creating detailed visual profiles that will
            ensure consistent representation across all generated images. You focus on physical appearance,
            clothing, and distinctive features that make each character recognizable.

            CHARACTER ANALYSIS CONTENT GUIDELINES:
            • Use professional, neutral terminology for all character descriptions
            • Focus on distinctive visual features: facial structure, hair, eye color, build, height
            • Emphasize clothing style, accessories, and distinctive visual elements
            • Use character archetype language: protagonist, antagonist, supporting character, etc.
            • Avoid descriptions that reference violence, injuries, or problematic content

            PREFERRED CHARACTER DESCRIPTION APPROACH:
            • Physical features: "distinctive facial features", "professional appearance", "athletic build"
            • Clothing: "formal attire", "casual wear", "professional uniform", "distinctive style"
            • Character roles: "story protagonist", "primary antagonist", "supporting character"
            • Emotional traits: "determined demeanor", "confident presence", "thoughtful expression"

            CONTENT-SAFE CHARACTER PROFILING:
            • Instead of injury references → "character under pressure", "intense expression"
            • Instead of weapon references → "professional equipment", "tactical gear"
            • Instead of violence references → "action-oriented character", "high-stakes role"
            • Focus on: personality traits, visual distinctiveness, professional roles, emotional characteristics

            Create character profiles that are visually distinctive and narratively consistent while using safe, professional language.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        # Prepare the story text for analysis
        story_text = f"Title: {story.title}\n\n"

        for scene in story.scenes:
            story_text += f"Scene {scene.scene_number}:\n"
            story_text += f"Narration: {scene.narration}\n"
            story_text += f"Visual Description: {scene.visual_description}\n\n"

        # Create the analysis task
        analysis_task = Task(
            description=f"""Analyze the following Hindi story and identify all characters that appear in it.
            Create detailed, content-safe character profiles for visual consistency.

            CONTENT-SAFE CHARACTER PROFILING REQUIREMENTS:

            For each character, create a detailed profile including:

            1. CHARACTER IDENTIFICATION:
               - Character name (as mentioned in story)
               - Role archetype: protagonist, antagonist, supporting character, etc.
               - Narrative function and importance to the story

            2. PHYSICAL DESCRIPTION (Content-Safe):
               - Age range and general build (athletic, average, tall, etc.)
               - Facial features: distinctive characteristics, facial structure
               - Hair: color, style, length, distinctive qualities
               - Eyes: color, expression, distinctive qualities
               - Skin tone and general appearance
               - Height and build using professional, neutral terms

            3. CLOTHING AND STYLE (Professional Descriptions):
               - Typical attire: formal, casual, professional, traditional
               - Clothing colors and style preferences
               - Accessories or distinctive clothing elements
               - Professional or cultural clothing indicators

            4. DISTINCTIVE VISUAL FEATURES:
               - Unique physical characteristics that make them recognizable
               - Facial expressions or demeanor
               - Posture and body language characteristics
               - Any distinctive accessories or visual elements

            5. CHARACTER PRESENCE AND DEMEANOR:
               - Personality traits that affect visual representation
               - Emotional characteristics and typical expressions
               - Professional or social role indicators
               - Character confidence, intensity, or other visual traits

            6. STORY CONTEXT:
               - Brief background relevant to visual representation
               - Character's role and importance in the narrative
               - List of scene numbers where the character appears

            CONTENT GUIDELINES FOR CHARACTER DESCRIPTIONS:
            • Use professional, neutral language for all descriptions
            • Focus on distinctive visual features and clothing
            • Emphasize character archetypes and roles
            • Avoid references to violence, injuries, or problematic content
            • Use cinematic and artistic terminology
            • Create descriptions suitable for professional character design

            Story to analyze:
            {story_text}

            Focus on characters that are actually described or mentioned in the story.
            Create detailed, content-safe descriptions that ensure visual consistency across all generated images.""",
            agent=character_analyzer,
            expected_output=format_instructions
        )

        # Execute the analysis
        crew = Crew(
            agents=[character_analyzer],
            tasks=[analysis_task],
            process=Process.sequential,
            verbose=self.verbose
        )

        result = crew.kickoff()

        # Parse the result using the Pydantic parser
        character_data = CharacterConsistencyDataParser.parse_output(parser, str(result))

        # If parsing fails, return empty data
        if character_data is None:
            logger.warning("Could not parse character analysis result, returning empty data")
            character_data = CharacterConsistencyData(
                characters=[],
                character_references=[],
                character_appearance_notes={},
                has_labeled_reference=False
            )

        logger.info(f"Character analysis complete. Found {len(character_data.characters)} characters")
        return character_data

    def generate_character_references(self,
                                      character_data: CharacterConsistencyData,
                                      output_dir: str,
                                      image_style: str = 'realistic') -> CharacterConsistencyData:
        """
        Generate reference images for all characters.

        This method creates individual reference images for each character identified
        in the story analysis. These images serve as visual references to maintain
        character consistency across all generated scene images.

        Args:
            character_data (CharacterConsistencyData): Character data from analysis containing
                character profiles and descriptions.
            output_dir (str): Directory to save reference images. A 'characters' subdirectory
                will be created if it doesn't exist.
            image_style (str): Style for reference images (e.g., 'realistic', 'anime', etc.).

        Returns:
            CharacterConsistencyData: Updated data with reference image paths and metadata
            for each successfully generated character reference.

        Note:
            Failed image generations are logged but don't stop the process. The method
            continues with remaining characters and returns partial results.
        """
        logger.info(f"Generating reference images for {len(character_data.characters)} characters")

        # Create characters subdirectory
        characters_dir = os.path.join(output_dir, 'characters')
        os.makedirs(characters_dir, exist_ok=True)

        # Initialize image generator
        image_generator = ImageGenerator(
            model=self.image_model,
            provider=self.image_provider,
            max_concurrent_requests=5,  # Lower concurrency for reference images
            use_parallel=False  # Sequential for reference images to ensure quality
        )

        updated_references = []

        for character in character_data.characters:
            logger.info(f"Generating reference image for character: {character.name}")

            # Create detailed prompt for character reference
            reference_prompt = self._create_character_reference_prompt(character, image_style)

            # Generate filename
            safe_name = "".join(c for c in character.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_name = safe_name.replace(' ', '_')
            filename = os.path.join(characters_dir, f"character_{safe_name}_reference.png")

            try:
                # Generate the reference image
                image_data = image_generator._generate_image_from_prompt(
                    reference_prompt,
                    width=1024,
                    height=1024,
                    image_style=image_style
                )

                if image_data:
                    # Save the image
                    with open(filename, "wb") as f:
                        f.write(image_data)

                    # Create reference entry
                    reference = CharacterReference(
                        character_name=character.name,
                        reference_image_path=filename,
                        image_prompt_used=reference_prompt,
                        generation_timestamp=datetime.datetime.now().isoformat()
                    )
                    updated_references.append(reference)

                    logger.info(f"Reference image saved for {character.name}: {filename}")

                else:
                    logger.error(f"Failed to generate reference image for {character.name}")

            except Exception as e:
                logger.error(f"Error generating reference image for {character.name}: {str(e)}")

        # Update character data with references
        character_data.character_references = updated_references

        logger.info(f"Generated {len(updated_references)} character reference images")
        return character_data

    def generate_group_image(self,
                             character_data: CharacterConsistencyData,
                             output_dir: str,
                             image_style: str = 'realistic') -> Optional[str]:
        """
        Generates a single labeled group reference image containing all characters from the story.

        This method creates a reference image optimized for use with flux-kontext-pro model,
        where each character is clearly labeled with their name for consistent scene generation.

        Args:
            character_data (CharacterConsistencyData): Character data from analysis.
            output_dir (str): Directory to save the group image.
            image_style (str): Style for the group image.

        Returns:
            Optional[str]: Path to the generated labeled group reference image, or None if generation fails.
        """
        if not character_data.characters:
            logger.warning("No characters found, skipping group image generation.")
            return None

        logger.info("Generating labeled group reference image for flux-kontext-pro character consistency.")

        # Create characters subdirectory if it doesn't exist
        characters_dir = os.path.join(output_dir, 'characters')
        os.makedirs(characters_dir, exist_ok=True)

        # Initialize image generator
        image_generator = ImageGenerator(
            model=self.image_model,
            provider=self.image_provider,
            max_concurrent_requests=1, # Single request for group image
            use_parallel=False
        )

        # Create character descriptions with explicit labeling requirements
        character_descriptions = []
        character_names = []
        for character in character_data.characters:
            # Apply content moderation to character descriptions
            sanitized_physical = sanitize_character_description(character.physical_description)
            sanitized_clothing = sanitize_character_description(character.clothing_style)
            sanitized_features = sanitize_character_description(character.distinctive_features)

            char_desc = f"{character.name}: {sanitized_physical}, {sanitized_clothing}, {sanitized_features}"
            character_descriptions.append(char_desc)
            character_names.append(character.name)

        # Create enhanced prompt for labeled reference image optimized for flux-kontext-pro
        group_prompt = f"""Professional character reference sheet featuring all main story characters in {image_style} style with clear name labels.

CHARACTER REFERENCE LINEUP:
{'. '.join(character_descriptions)}.

LABELED REFERENCE IMAGE SPECIFICATIONS FOR FLUX-KONTEXT-PRO:
- Professional studio composition with all characters clearly visible and well-separated
- Each character positioned distinctly with ample space between them
- Clear, readable name labels positioned directly below or above each character
- Character names "{', '.join(character_names)}" must be clearly visible as text labels in the image
- Consistent {image_style} style rendering across all characters
- Professional lighting ensuring each character and their name label is well-defined
- High-contrast text labels for character names using clean, readable font
- Reference-quality character design suitable for consistent scene generation
- Landscape orientation (16:9 aspect ratio) for optimal reference usage
- Each character maintains their distinctive visual characteristics for easy identification

CRITICAL REQUIREMENT: Each character must have their name clearly labeled in the image for reference-based generation."""

        # Generate filename
        filename = os.path.join(characters_dir, "labeled_characters_reference.png")

        try:
            image_data = image_generator._generate_image_from_prompt(
                group_prompt,
                width=1920, # Landscape format for reference image
                height=1080,
                image_style=image_style
            )

            if image_data:
                with open(filename, "wb") as f:
                    f.write(image_data)

                logger.info(f"Labeled group reference image saved to {filename}")
                logger.info(f"Reference image contains labeled characters: {', '.join(character_names)}")
                return filename

            else:
                logger.error("Failed to generate labeled group reference image.")
                return None

        except Exception as e:
            logger.error(f"Error generating labeled group reference image: {str(e)}")
            return None

    def _create_character_reference_prompt(self, character: Character, image_style: str) -> str:
        """
        Create a detailed, content-safe prompt for generating a character reference image.

        Args:
            character (Character): Character to create prompt for
            image_style (str): Image style to use

        Returns:
            str: Detailed, content-safe image prompt
        """
        # Apply content moderation to character descriptions
        sanitized_physical = sanitize_character_description(character.physical_description)
        sanitized_clothing = sanitize_character_description(character.clothing_style)
        sanitized_features = sanitize_character_description(character.distinctive_features)

        prompt = f"""Professional character reference sheet for {character.name}, {character.role} archetype in the story.

        VISUAL CHARACTERISTICS:
        Physical Appearance: {sanitized_physical}
        Clothing Style: {sanitized_clothing}
        Distinctive Features: {sanitized_features}

        CHARACTER REFERENCE SPECIFICATIONS:
        - Professional character portrait with clear, detailed features
        - Front-facing view optimized for character consistency
        - {image_style} style rendering with high-quality character design
        - Consistent studio lighting with neutral background
        - Full body or upper body composition suitable for reference purposes
        - Professional character design standards with clear visual details
        - Cinematic character presentation emphasizing distinctive features
        - Character archetype representation maintaining story authenticity

        Focus on creating a professional character reference that captures the character's essence
        while maintaining visual consistency for future scene generation."""

        return prompt

    def process_story_for_consistency(self,
                                      story: Story,
                                      output_dir: str,
                                      image_style: str = 'realistic',
                                      generate_individual_references: bool = False) -> CharacterConsistencyData:
        """
        Complete character consistency processing pipeline.

        This method performs the full character consistency workflow including character
        analysis and reference image generation. Individual character references are
        optional since only the group image is used in the main pipeline.

        Args:
            story (Story): Story to process for character analysis.
            output_dir (str): Output directory for character data and images.
            image_style (str): Style for reference images (e.g., 'realistic', 'anime').
            generate_individual_references (bool): Whether to generate individual character
                reference images. Defaults to False since only group image is used in pipeline.

        Returns:
            CharacterConsistencyData: Complete character consistency data with group image path.

        Note:
            Individual character references are not used in the main image generation pipeline.
            Only the group image serves as the visual reference for character consistency.
        """
        logger.info("Starting complete character consistency processing")

        # Step 1: Analyze characters
        character_data = self.analyze_characters(story)

        if character_data.characters:
            # Step 2: Generate individual reference images (optional)
            if generate_individual_references:
                logger.info("Generating individual character reference images")
                character_data = self.generate_character_references(character_data, output_dir, image_style)
            else:
                logger.info("Skipping individual character references (not used in pipeline)")

            # Step 3: Generate group image (primary reference for pipeline)
            group_image_path = self.generate_group_image(character_data, output_dir, image_style)

            if group_image_path:
                character_data.characters_group_image_path = group_image_path
                character_data.has_labeled_reference = True  # Mark as labeled reference for flux-kontext-pro
                logger.info(f"Labeled group reference image generated: {group_image_path}")
                logger.info("Reference image optimized for flux-kontext-pro character consistency")
            else:
                logger.warning("Failed to generate labeled group reference image")

        else:
            logger.warning("No characters found in story, skipping reference image generation")

        # Step 4: Save character data
        character_data_path = os.path.join(output_dir, 'character_consistency.json')

        with open(character_data_path, 'w', encoding='utf-8') as f:
            json.dump(character_data.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"Character consistency data saved to {character_data_path}")
        logger.info("Character consistency processing complete")

        return character_data
